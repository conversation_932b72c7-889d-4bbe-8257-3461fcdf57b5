﻿using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.CustomModel;

namespace RealEstate.InternalAPI.Controllers
{
    
    /// <summary>
    /// Controller for internal user management operations such as roles, dashboard, reactivation, and invoice info.
    /// </summary>
    [Route("api/[controller]")]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, IUserDashboardService dashboardService,
        ILogger<UserController> logger)
        {
            _userService = userService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        /// <summary>
        /// Retrieves user information by user ID (internal use).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User information.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            _logger?.LogInformation("[GetUser] Retrieving user information for user {UserId}", id);
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    _logger?.LogWarning("[GetUser] User {UserId} not found", id);
                    return NotFound();
                }
                _logger?.LogInformation("[GetUser] Successfully retrieved user information for user {UserId}", id);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUser] Error retrieving user {UserId}", id);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves the dashboard for a user by user ID (internal use).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>User dashboard data.</returns>
        [HttpGet("dashboard/{userId}")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboardById(Guid userId)
        {
            _logger?.LogInformation("[GetUserDashboardById] Retrieving dashboard for user {UserId}", userId);
            try
            {
                var dashboard = await _dashboardService.GetUserDashboardAsync(userId);
                _logger?.LogInformation("[GetUserDashboardById] Successfully retrieved dashboard for user {UserId}", userId);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                _logger?.LogWarning("[GetUserDashboardById] Dashboard not found for user {UserId}: {Message}", userId, ex.Message);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUserDashboardById] Error retrieving dashboard for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Reactivates a user account (admin only).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>Status of the reactivation.</returns>
        [HttpPost("reactivate/{userId}")]
        public async Task<ActionResult> ReactivateAccount(Guid userId)
        {
            _logger?.LogInformation("[ReactivateAccount] Attempting to reactivate account for user {UserId}", userId);
            try
            {
                var result = await _userService.ReactivateUserAsync(userId);
                if (!result)
                {
                    _logger?.LogWarning("[ReactivateAccount] Failed to reactivate account for user {UserId}", userId);
                    return BadRequest("Failed to reactivate account. User not found.");
                }
                _logger?.LogInformation("[ReactivateAccount] Account has been reactivated for user {UserId}", userId);
                return Ok(new { Message = "Account has been reactivated successfully." });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[ReactivateAccount] Error reactivating account for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }        

        /// <summary>
        /// Retrieves invoice information for a user (admin only).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User invoice information.</returns>
        [HttpGet("{id}/invoice-info")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserInvoiceInfo(Guid id)
        {
            _logger?.LogInformation("[GetUserInvoiceInfo] Retrieving invoice info for user {UserId}", id);
            try
            {
                var invoiceInfo = await _userService.GetUserInvoiceInfoAsync(id);
                if (invoiceInfo == null)
                {
                    _logger?.LogWarning("[GetUserInvoiceInfo] User {UserId} not found", id);
                    return NotFound("User not found");
                }
                _logger?.LogInformation("[GetUserInvoiceInfo] Successfully retrieved invoice info for user {UserId}", id);
                return Ok(invoiceInfo);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUserInvoiceInfo] Error retrieving invoice info for user {UserId}", id);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Get all non-admin users with pagination and filtering
        /// </summary>
        /// <param name="filter">Filter criteria including pagination, email, name, phone filters</param>
        /// <returns>Paginated list of non-admin users</returns>
        [HttpGet("non-admin")]
        public async Task<ActionResult<PagedResultDto<UserDto>>> GetNonAdminUsers([FromQuery] UserFilterDto filter)
        {
            _logger?.LogInformation("[GetNonAdminUsers] Retrieving non-admin users with filter: {@Filter}", filter);
            try
            {
                var users = await _userService.GetNonAdminUsersAsync(filter);
                _logger?.LogInformation("[GetNonAdminUsers] Successfully retrieved {Count} non-admin users", users.Items.Count);
                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetNonAdminUsers] Error retrieving non-admin users");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Get all admin users (employees) with pagination and filtering
        /// </summary>
        /// <param name="filter">Filter criteria including pagination, email, name, phone filters</param>
        /// <returns>Paginated list of admin users</returns>
        [HttpGet("admin")]
        public async Task<ActionResult<PagedResultDto<UserDto>>> GetAdminUsers([FromQuery] UserFilterDto filter)
        {
            _logger?.LogInformation("[GetAdminUsers] Retrieving admin users with filter: {@Filter}", filter);
            try
            {
                var users = await _userService.GetAdminUsersAsync(filter);
                _logger?.LogInformation("[GetAdminUsers] Successfully retrieved {Count} admin users", users.Items.Count);
                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetAdminUsers] Error retrieving admin users");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Activate or deactivate a user account
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="updateStatusDto">The status update data</param>
        /// <returns>Status of the operation</returns>
        [HttpPut("{userId}/status")]
        public async Task<ActionResult> UpdateUserStatus(Guid userId, [FromBody] UpdateUserStatusDto updateStatusDto)
        {
            _logger?.LogInformation("[UpdateUserStatus] Updating status for user {UserId} to {IsActive}", userId, updateStatusDto.IsActive);
            try
            {
                var result = await _userService.UpdateUserStatusAsync(userId, updateStatusDto.IsActive);
                if (!result)
                {
                    _logger?.LogWarning("[UpdateUserStatus] User {UserId} not found", userId);
                    return NotFound("User not found");
                }

                var statusText = updateStatusDto.IsActive ? "activated" : "deactivated";
                _logger?.LogInformation("[UpdateUserStatus] Successfully {StatusText} user {UserId}", statusText, userId);
                return Ok(new { Message = $"User has been {statusText} successfully" });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UpdateUserStatus] Error updating status for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Create a new admin user (employee) with roles
        /// </summary>
        /// <param name="createAdminUserDto">The admin user creation data</param>
        /// <returns>The created admin user</returns>
        [HttpPost("admin")]
        public async Task<ActionResult<UserDto>> CreateAdminUser([FromBody] CreateAdminUserDto createAdminUserDto)
        {
            _logger?.LogInformation("[CreateAdminUser] Creating admin user with email {Email}", createAdminUserDto.Email);
            try
            {
                var user = await _userService.CreateAdminUserAsync(createAdminUserDto);
                _logger?.LogInformation("[CreateAdminUser] Successfully created admin user {UserId} with email {Email}", user.Id, user.Email);
                return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
            }
            catch (InvalidOperationException ex)
            {
                _logger?.LogWarning("[CreateAdminUser] Invalid operation: {Message}", ex.Message);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[CreateAdminUser] Error creating admin user with email {Email}", createAdminUserDto.Email);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Update user roles
        /// </summary>
        /// <param name="updateUserRoleDto">The user role update data</param>
        /// <returns>Status of the operation</returns>
        [HttpPut("roles")]
        public async Task<ActionResult> UpdateUserRoles([FromBody] UpdateUserRoleDto updateUserRoleDto)
        {
            _logger?.LogInformation("[UpdateUserRoles] Updating roles for user {UserId}", updateUserRoleDto.UserId);
            try
            {
                var result = await _userService.UpdateUserRolesAsync(updateUserRoleDto);
                if (!result)
                {
                    _logger?.LogWarning("[UpdateUserRoles] Failed to update roles for user {UserId}", updateUserRoleDto.UserId);
                    return BadRequest("Failed to update user roles");
                }

                _logger?.LogInformation("[UpdateUserRoles] Successfully updated roles for user {UserId}", updateUserRoleDto.UserId);
                return Ok(new { Message = "User roles updated successfully" });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[UpdateUserRoles] Error updating roles for user {UserId}", updateUserRoleDto.UserId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
