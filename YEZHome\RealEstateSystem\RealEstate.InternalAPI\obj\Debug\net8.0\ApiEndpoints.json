[{"ContainingType": "RealEstate.InternalAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "RealEstate.Application.DTO.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.BlogController", "Method": "CreateBlog", "RelativePath": "api/Blog", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "blogDto", "Type": "RealEstate.Application.DTO.CreateBlogPostDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.BlogPostDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.BlogController", "Method": "GetBlogPostById", "RelativePath": "api/Blog/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.BlogPostDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.BlogController", "Method": "UpdateBlog", "RelativePath": "api/Blog/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "blogDto", "Type": "RealEstate.Application.DTO.CreateBlogPostDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.BlogController", "Method": "DeleteBlog", "RelativePath": "api/Blog/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.NotificationController", "Method": "Create", "RelativePath": "api/Notification", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createNotificationDto", "Type": "RealEstate.Application.DTO.Notification.CreateNotificationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.Notification.NotificationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.NotificationController", "Method": "GetByTypeAndUser", "RelativePath": "api/Notification/by-type/{type}/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.Notification.NotificationDto, RealEstate.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "GetPropertyById", "RelativePath": "api/Property/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "DeleteProperty", "RelativePath": "api/Property/{propertyId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "GetPropertyHistoryStatus", "RelativePath": "api/Property/{propertyId}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.PropertyStatusLogDto, RealEstate.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "UpdateStatus", "RelativePath": "api/Property/{propertyId}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "RealEstate.Application.DTO.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "DeleteProperties", "RelativePath": "api/Property/bulk", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.BulkPropertyIdsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "UpdateStatusBulk", "RelativePath": "api/Property/bulk/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.BulkUpdateStatusDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "GetPropertyCountByStatus", "RelativePath": "api/Property/count-by-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyCountStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "SearchProperties", "RelativePath": "api/Property/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "propertyType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.PropertyController", "Method": "CountProperties", "RelativePath": "api/Property/search/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "propertyType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "GetUser", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "GetUserInvoiceInfo", "RelativePath": "api/User/{id}/invoice-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserInvoiceInfoDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "UpdateUserStatus", "RelativePath": "api/User/{userId}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateStatusDto", "Type": "RealEstate.Application.DTO.UpdateUserStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "GetAdminUsers", "RelativePath": "api/User/admin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "UserType", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortColumn", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.UserDto, RealEstate.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "CreateAdminUser", "RelativePath": "api/User/admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createAdminUserDto", "Type": "RealEstate.Application.DTO.CreateAdminUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "GetUserDashboardById", "RelativePath": "api/User/dashboard/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDashboardDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "GetNonAdminUsers", "RelativePath": "api/User/non-admin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "UserType", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortColumn", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.UserDto, RealEstate.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "ReactivateAccount", "RelativePath": "api/User/reactivate/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.InternalAPI.Controllers.UserController", "Method": "UpdateUserRoles", "RelativePath": "api/User/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateUserRoleDto", "Type": "RealEstate.Application.DTO.UpdateUserRoleDto", "IsRequired": true}], "ReturnTypes": []}]